data_qa_sys_prompt = """
你是一名项目管理时间节点查询专家，需严格按以下规则处理用户问题：

处理流程
1. 识别项目编码（格式如P81项目）

2. 解析里程碑名称（支持行话映射）：

- 挂板/发板→投板 KO→整机KO 爬坡→国际爬坡/国内爬坡 | Px直接匹配
3. 在JSON知识库中：

- 用项目编码匹配项目编码字段（如P81匹配K19-CAM）
- 用映射后的术语匹配里程碑名称
4. 返回结果：

- 单节点：返回计划开始时间（仅日期部分）
- 多节点：按时间升序列出所有节点
- 异常：明确提示未找到项目/节点
"""



data_qa_user_prompt = """
  ---
  从知识库中搜索到的相关信息：  
{
    "项目编码": "M18",
    "里程碑": [
        {
            "里程碑名称": "立项",
            "计划开始时间": "2022-07-29 08:00:00.000"
        },
        {
            "里程碑名称": "堆叠锁定",
            "计划开始时间": "2022-10-08 08:00:00.000"
        },
        {
            "里程碑名称": "KO",
            "计划开始时间": "2022-11-02 00:00:00.000"
        },
        {
            "里程碑名称": "投板",
            "计划开始时间": "2022-11-23 08:00:00.000"
        },
        {
            "里程碑名称": "投模",
            "计划开始时间": "2022-11-30 08:00:00.000"
        },
        {
            "里程碑名称": "P0",
            "计划开始时间": "2022-12-12 08:00:00.000"
        },
        {
            "里程碑名称": "P0.1",
            "计划开始时间": "2023-01-03 08:00:00.000"
        },
        {
            "里程碑名称": "P1",
            "计划开始时间": "2023-02-19 08:00:00.000"
        },
        {
            "里程碑名称": "P1.1",
            "计划开始时间": "2023-04-01 08:00:00.000"
        },
        {
            "里程碑名称": "P2",
            "计划开始时间": "2023-05-16 08:00:00.000"
        },
        {
            "里程碑名称": "P2.1",
            "计划开始时间": "2023-06-18 08:00:00.000"
        },
        {
            "里程碑名称": "国内爬坡",
            "计划开始时间": "2023-07-02 08:00:00.000"
        },
        {
            "里程碑名称": "发布",
            "计划开始时间": "2023-08-11 08:00:00.000"
        },
        {
            "里程碑名称": "EOP",
            "计划开始时间": "2024-06-01 08:00:00.000"
        },
        {
            "里程碑名称": "TR3",
            "计划开始时间": "2022-12-07 08:00:00.000"
        },
        {
            "里程碑名称": "TR4",
            "计划开始时间": "2023-01-10 08:00:00.000"
        },
        {
            "里程碑名称": "TR4A-国内",
            "计划开始时间": "2023-05-11 08:00:00.000"
        },
        {
            "里程碑名称": "TR5-国内",
            "计划开始时间": "2023-06-21 08:00:00.000"
        },
        {
            "里程碑名称": "TR6-国内",
            "计划开始时间": "2023-07-24 08:00:00.000"
        }
    ]
}

{
    "项目编码": "M9",
    "里程碑": [
        {
            "里程碑名称": "立项",
            "计划开始时间": "2022-07-04 08:00:00.000"
        },
        {
            "里程碑名称": "堆叠锁定",
            "计划开始时间": "2022-09-29 08:00:00.000"
        },
        {
            "里程碑名称": "KO",
            "计划开始时间": "2022-10-31 08:00:00.000"
        },
        {
            "里程碑名称": "投板",
            "计划开始时间": "2022-11-30 08:00:00.000"
        },
        {
            "里程碑名称": "投模",
            "计划开始时间": "2022-12-05 08:00:00.000"
        },
        {
            "里程碑名称": "P0",
            "计划开始时间": "2022-12-11 08:00:00.000"
        },
        {
            "里程碑名称": "P0.1",
            "计划开始时间": "2023-01-02 08:00:00.000"
        },
        {
            "里程碑名称": "P1",
            "计划开始时间": "2023-01-15 08:00:00.000"
        },
        {
            "里程碑名称": "P1.1",
            "计划开始时间": "2023-02-12 08:00:00.000"
        },
        {
            "里程碑名称": "P2",
            "计划开始时间": "2023-02-24 08:00:00.000"
        },
        {
            "里程碑名称": "国内爬坡",
            "计划开始时间": "2023-03-31 08:00:00.000"
        },
        {
            "里程碑名称": "国内封包",
            "计划开始时间": "2023-04-25 08:00:00.000"
        },
        {
            "里程碑名称": "包装",
            "计划开始时间": "2023-05-04 08:00:00.000"
        },
        {
            "里程碑名称": "发布",
            "计划开始时间": "2023-05-20 08:00:00.000"
        },
        {
            "里程碑名称": "EOP",
            "计划开始时间": "2024-03-01 18:54:58.000"
        },
        {
            "里程碑名称": "TR1",
            "计划开始时间": "2022-09-29 08:00:00.000"
        },
        {
            "里程碑名称": "TR2",
            "计划开始时间": "2022-10-31 08:00:00.000"
        },
        {
            "里程碑名称": "TR3",
            "计划开始时间": "2022-11-28 08:00:00.000"
        },
        {
            "里程碑名称": "TR4",
            "计划开始时间": "2023-01-13 08:00:00.000"
        },
        {
            "里程碑名称": "TR4A-国内",
            "计划开始时间": "2023-02-20 08:00:00.000"
        },
        {
            "里程碑名称": "TR5-国内",
            "计划开始时间": "2023-03-22 08:00:00.000"
        },
        {
            "里程碑名称": "TR6-国内",
            "计划开始时间": "2023-05-11 08:00:00.000"
        }
    ]
}
  ---
  用户问题：  
  {{query}}
""" 