#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
"""

import sys
import os
import asyncio
import json
import uuid
import httpx
import argparse
from typing import Dict, Any, List, Optional
import traceback

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
if os.path.exists(env_file):
    load_dotenv(env_file)
    print(f"已加载环境配置: {env_file}")
else:
    # 如果特定环境的配置文件不存在，尝试加载默认的.env文件
    if os.path.exists(".env"):
        load_dotenv(".env")
        print("已加载默认环境配置: .env")
    else:
        print(f"未找到环境配置文件: {env_file} 或 .env")

# 默认API地址
DEFAULT_API_URL = "http://localhost:8080/api/v1"

async def test_health(api_url: str, timeout: float = 30.0) -> bool:
    """测试健康检查接口"""
    url = f"{api_url}/health"
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(url)
            response.raise_for_status()
            result = response.json()
            print(f"健康检查结果: {result}")
            return True
    except Exception as e:
        print(f"健康检查失败: {str(e)}")
        print(f"异常类型: {type(e)}")
        traceback.print_exc()
        return False

async def test_llm_qa(api_url: str, stream: bool = False, timeout: float = 600.0) -> None:
    """测试LLM问答接口"""
    url = f"{api_url}/llm-qa"
    request_id = str(uuid.uuid4())
    conversation_id = str(uuid.uuid4())
    payload = {
        "query": "库存周转率如何计算？",
        "user_id": "test_user",
        "model_id": "qwen3_32b",
        "msg_id": request_id,
        "conversation_id": conversation_id,
        "history": [
            {"role": "user", "content": "什么是供应链管理？"}, 
            {"role": "assistant", "content": "供应链管理是指对供应链中的信息流、物流和资金流进行计划、组织、协调与控制的过程。"}
        ],
        "stream": stream
    }
    
    try:
        if stream:
            # 流式响应处理
            async with httpx.AsyncClient(timeout=timeout) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    print("\n流式响应开始:")
                    # 收集完整的响应内容
                    full_content = ""
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                # 尝试解析JSON
                                chunk = json.loads(data)
                                print(f"收到数据: {chunk}")
                            except json.JSONDecodeError:
                                # 如果无法解析，则打印原始数据
                                print(f"收到数据块: {data}")
                                # 收集内容以便后续分析
                                full_content += data
                    print("流式响应结束\n")
                    # 打印收集到的完整内容
                    if full_content:
                        print(f"收集到的完整内容:\n{full_content}\n")
        else:
            # 非流式响应处理
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(url, json=payload)
                response.raise_for_status()
                result = response.json()
                print(f"\nLLM问答结果:\n{json.dumps(result, ensure_ascii=False, indent=2)}\n")
    except Exception as e:
        import traceback
        print(f"异常类型: {type(e)}")
        traceback.print_exc()
        # 增加详细异常打印
        if hasattr(e, 'response') and e.response is not None:
            try:
                print(f"LLM问答测试失败: {str(e)}，响应内容: {e.response.text}")
            except Exception:
                print(f"LLM问答测试失败: {str(e)}，响应内容无法解码")
        else:
            print(f"LLM问答测试失败: {str(e)}")

async def test_rag_qa(api_url: str, stream: bool = False, timeout: float = 600.0) -> None:
    """测试RAG问答接口"""
    url = f"{api_url}/rag-qa"
    request_id = str(uuid.uuid4())
    conversation_id = str(uuid.uuid4())
    # 复合应力导致 FPC 断线案例
    payload = {
        "query": "复合应力导致 FPC 断线案例",
        "user_id": "test_user",
        "model_id": "qwen3_32b",
        "msg_id": request_id,
        "conversation_id": conversation_id,
        "history": [
            {"role": "user", "content": "PCB是什么？"}, 
            {"role": "assistant", "content": "PCB是印刷电路板(Printed Circuit Board)的缩写，是电子元器件的支撑体和电气连接的载体。"}
        ],
        "stream": stream,
        "top_k": 3
    }
    try:
        if stream:
            # 流式响应处理
            async with httpx.AsyncClient(timeout=timeout) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    print("\n流式响应开始:")
                    # 收集完整的响应内容
                    full_content = ""
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            print(f"收到数据块: {line}")
                            data = line[6:]
                            try:
                                # 尝试解析JSON
                                chunk = json.loads(data)
                                # print(f"收到数据: {chunk}")
                            except json.JSONDecodeError:
                                # 如果无法解析，则打印原始数据
                                # print(f"收到数据块: {data}")
                                # 收集内容以便后续分析
                                full_content += data
                    print("流式响应结束\n")
                    # 打印收集到的完整内容
                    if full_content:
                        print(f"收集到的完整内容:\n{full_content}\n")
        else:
            # 非流式响应处理
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(url, json=payload)
                response.raise_for_status()
                result = response.json()
                print(f"\nRAG问答结果:\n{json.dumps(result, ensure_ascii=False, indent=2)}\n")
    except Exception as e:
        import traceback
        print(f"异常类型: {type(e)}")
        traceback.print_exc()
        # 增加详细异常打印
        if hasattr(e, 'response') and e.response is not None:
            try:
                print(f"RAG问答测试失败: {str(e)}，响应内容: {e.response.text}")
            except Exception:
                print(f"RAG问答测试失败: {str(e)}，响应内容无法解码")
        else:
            print(f"RAG问答测试失败: {str(e)}")

async def test_error_handling(api_url: str, timeout: float = 30.0) -> None:
    """测试错误处理"""
    url = f"{api_url}/llm-qa"
    # 缺少必要参数
    payload = {
        "user_id": "test_user",
        "model_id": "qwen3_32b"
        # 缺少query参数
    }
    
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(url, json=payload)
            print(f"\n错误处理测试结果: 状态码={response.status_code}")
            print(f"响应内容:\n{json.dumps(response.json(), ensure_ascii=False, indent=2)}\n")
    except Exception as e:
        print(f"错误处理测试失败: {str(e)}")
        print(f"异常类型: {type(e)}")
        traceback.print_exc()

async def test_data_qa(api_url: str, stream: bool = False, timeout: float = 600.0) -> None:
    """测试DATAQA问答接口"""
    url = f"{api_url}/data-qa"
    request_id = str(uuid.uuid4())
    conversation_id = str(uuid.uuid4())
    payload = {
        "query": "M9项目什么时候需要挂板？",
        "user_id": "test_user",
        "model_id": "qwen3_32b",
        "msg_id": request_id,
        "conversation_id": conversation_id,
        "history": [
            {"role": "user", "content": "什么是FPC？"},
            {"role": "assistant", "content": "FPC是柔性印刷电路板..."}
        ],
        "stream": stream,
        "top_k": 1
    }
    try:
        if stream:
            async with httpx.AsyncClient(timeout=timeout) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    print("\n流式响应开始:")
                    full_content = ""
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                print(f"收到数据: {chunk}")
                            except json.JSONDecodeError:
                                print(f"收到数据块: {data}")
                                full_content += data
                    print("流式响应结束\n")
                    if full_content:
                        print(f"收集到的完整内容:\n{full_content}\n")
        else:
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(url, json=payload)
                response.raise_for_status()
                result = response.json()
                print(f"\nDATAQA问答结果:\n{json.dumps(result, ensure_ascii=False, indent=2)}\n")
    except Exception as e:
        import traceback
        print(f"异常类型: {type(e)}")
        traceback.print_exc()
        if hasattr(e, 'response') and e.response is not None:
            try:
                print(f"DATAQA问答测试失败: {str(e)}，响应内容: {e.response.text}")
            except Exception:
                print(f"DATAQA问答测试失败: {str(e)}，响应内容无法解码")
        else:
            print(f"DATAQA问答测试失败: {str(e)}")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="API测试脚本")
    parser.add_argument(
        "--api-url", 
        type=str, 
        default=DEFAULT_API_URL, 
        help=f"API地址，默认为{DEFAULT_API_URL}"
    )
    parser.add_argument(
        "--test-all", 
        action="store_true", 
        help="测试所有接口"
    )
    parser.add_argument(
        "--test-health", 
        action="store_true", 
        help="测试健康检查接口"
    )
    parser.add_argument(
        "--test-llm", 
        action="store_true", 
        help="测试LLM问答接口"
    )
    parser.add_argument(
        "--test-rag", 
        action="store_true", 
        help="测试RAG问答接口"
    )
    parser.add_argument(
        "--test-error", 
        action="store_true", 
        help="测试错误处理"
    )
    parser.add_argument(
        "--stream", 
        action="store_true", 
        help="使用流式响应"
    )
    parser.add_argument(
        "--timeout", 
        type=float, 
        default=600.0, 
        help="请求超时时间（秒），默认为600秒（10分钟）"
    )
    
    args = parser.parse_args()
    
    # 如果没有指定任何测试，则默认测试所有接口
    if not (args.test_all or args.test_health or args.test_llm or args.test_rag or args.test_error):
        args.test_all = True
    
    # 测试健康检查接口
    if args.test_all or args.test_health:
        health_ok = await test_health(args.api_url, args.timeout)
        if not health_ok and args.test_all:
            print("健康检查失败，跳过其他测试")
            return
    
    # 测试LLM问答接口
    # if args.test_all or args.test_llm:
    #     await test_llm_qa(args.api_url, args.stream, args.timeout)
    
    # 测试RAG问答接口
    # if args.test_all or args.test_rag:
    #     await test_rag_qa(args.api_url, args.stream, args.timeout)
    
    # # 测试错误处理
    # if args.test_all or args.test_error:
    #     await test_error_handling(args.api_url, args.timeout)

    # 测试DATAQA问答接口
    await test_data_qa(args.api_url, args.stream, args.timeout)

if __name__ == "__main__":
    asyncio.run(main())