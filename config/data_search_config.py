import os
#search_mode: 搜索模式，可指定为text, dense, sparse, hybrid, hybrid_rrf，不指定时，默认使用dense模式
DATA_SEARCH_MODELS = {
    "default_search": {
        "api_url": os.getenv("SEARCH_API_URL", "http://localhost:8000"),
        "collection_name": os.getenv("SEARCH_COLLECTION", "default_collection"),
        "default_params": {
            "top_k": 1,
            "search_mode": "hybrid_rrf",
            "fusion_method": "rrf",
            "sparse_weight": 0.3,
            "dense_weight": 0.7,
            "rrf_k": 60
        }
    }
}
CURRENT_DATA_SEARCH_MODEL = os.getenv("SEARCH_MODEL", "default_search")
DATA_SEARCH_MODEL_CONFIG = DATA_SEARCH_MODELS[CURRENT_DATA_SEARCH_MODEL]